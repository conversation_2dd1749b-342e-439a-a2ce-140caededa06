# 🛡️ Система фалбеку для обробки PROHIBITED_CONTENT

## 📋 Огляд

Наша система фалбеку автоматично обробляє помилки `PROHIBITED_CONTENT` та інші проблеми з основною моделлю Gemini, переключаючись на молодшу модель з ретраями.

## 🔧 Як це працює

### 1. **Основний потік**
```
Запит → Gemini 2.5 Flash (основна модель)
  ↓ (успіх)
✅ Повертаємо результат
```

### 2. **Фалбек при PROHIBITED_CONTENT**
```
Запит → Gemini 2.5 Flash (основна модель)
  ↓ (PROHIBITED_CONTENT)
Запит → Gemini 2.5 Flash Lite (фалбек модель)
  ↓ (ретрай 1)
Запит → Gemini 2.5 Flash Lite (фалбек модель)
  ↓ (ретрай 2)
Запит → Gemini 2.5 Flash Lite (фалбек модель)
  ↓ (ретрай 3)
❌ Повертаємо помилку з поясненням
```

## ⚙️ Конфігурація

```typescript
const PRIMARY_MODEL = "gemini-2.5-flash";
const FALLBACK_MODEL = "gemini-2.5-flash-lite";
const MAX_FALLBACK_RETRIES = 3;
```

## 🎯 Типи помилок

### **PROHIBITED_CONTENT**
- Контент заблокований фільтрами безпеки
- Автоматично переключається на фалбек модель
- Показує спеціальне повідомлення користувачу

### **Критичні помилки**
- API ключ недійсний
- Проблеми з біллінгом
- Проблеми з аутентифікацією
- **Не ретраїться** - показує помилку одразу

### **Інші помилки**
- Тимчасові проблеми з мережею
- Перевантаження сервера
- Ретраїться з фалбек моделлю

## 📊 Статистика та моніторинг

### **Відстежувані метрики**
- `primarySuccess` - успішні запити основної моделі
- `fallbackSuccess` - успішні запити фалбек моделі
- `prohibitedContent` - кількість заблокованого контенту
- `totalFailures` - загальна кількість невдач

### **Події для UI**
```typescript
// Використання фалбек моделі
window.addEventListener('fallbackUsed', (event) => {
  console.log(event.detail); // { reason, attempt, model }
});

// Повна невдача перекладу
window.addEventListener('translationFailed', (event) => {
  console.log(event.detail); // { reason, attempts, originalError, lastError }
});

// Використання моделі
window.addEventListener('modelUsed', (event) => {
  console.log(event.detail); // { model, attempt }
});
```

## 🧪 Тестування

### **В консолі браузера**
```javascript
// Симулювати PROHIBITED_CONTENT
testFallback.simulateProhibitedContent();

// Симулювати повну невдачу
testFallback.simulateTranslationFailure();

// Симулювати успіх основної моделі
testFallback.simulatePrimarySuccess();
```

## 💡 Переваги системи

1. **Автоматичне відновлення** - без втручання користувача
2. **Розумні ретраї** - експоненційна затримка між спробами
3. **Детальна статистика** - моніторинг використання моделей
4. **Користувацькі повідомлення** - зрозумілі пояснення помилок
5. **Оптимізація витрат** - використання дешевшої моделі для фалбеку

## 🔄 Алгоритм ретраїв

```typescript
for (let attempt = 1; attempt <= 3; attempt++) {
  try {
    // Спроба з фалбек моделлю
    return await fallbackModel.translate();
  } catch (error) {
    if (isCriticalError(error)) break;
    
    // Експоненційна затримка: 1s, 2s, 4s (макс 5s)
    const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
    await sleep(delay);
  }
}
```

## 🎨 UI компоненти

### **ModelStatsDisplay**
- Показує статистику використання моделей
- Відображає останні помилки та фалбеки
- Можливість скидання статистики

### **Повідомлення користувачу**
- **PROHIBITED_CONTENT**: "Content was blocked by safety filters. Please try rephrasing your text or using a different translation style."
- **Загальна невдача**: "Translation failed after 3 attempts with fallback model. Please try again later."
- **Критична помилка**: Показує конкретну помилку

## 🚀 Майбутні покращення

1. **Адаптивні ретраї** - збільшення кількості спроб для певних типів помилок
2. **Кешування фалбеків** - збереження успішних фалбек перекладів
3. **A/B тестування** - порівняння ефективності різних стратегій
4. **Метрики продуктивності** - час відповіді, успішність по типах контенту
