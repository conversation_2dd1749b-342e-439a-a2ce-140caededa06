import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Language {
  code: string;
  name: string;
  flag: string;
}

interface LanguageDetectionButtonProps {
  isDetecting: boolean;
  detectedLanguage: string | null;
  onDetect: () => void;
  onLanguageDetected: (language: Language) => void;
  availableLanguages: Language[];
  disabled?: boolean;
  autoDetectEnabled?: boolean;
}

export const LanguageDetectionButton = ({
  isDetecting,
  detectedLanguage,
  onDetect,
  onLanguageDetected,
  availableLanguages,
  disabled = false,
  autoDetectEnabled = true
}: LanguageDetectionButtonProps) => {
  const { toast } = useToast();
  const [lastDetectedLanguage, setLastDetectedLanguage] = useState<string | null>(null);

  const handleDetect = () => {
    if (disabled || isDetecting) return;
    onDetect();
  };

  // Коли мова визначена, знаходимо відповідний об'єкт мови
  useEffect(() => {
    if (detectedLanguage && !isDetecting && detectedLanguage !== lastDetectedLanguage) {
      const foundLanguage = availableLanguages.find(lang => lang.code === detectedLanguage);
      if (foundLanguage) {
        // Викликаємо callback з затримкою, щоб дати час на відображення результату
        setTimeout(() => {
          onLanguageDetected(foundLanguage);
          toast({
            title: "Language Detected",
            description: `Detected: ${foundLanguage.name} ${foundLanguage.flag}`,
          });
          setLastDetectedLanguage(detectedLanguage);
        }, 500);
      }
    }
  }, [detectedLanguage, isDetecting, lastDetectedLanguage, availableLanguages, onLanguageDetected, toast]);

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleDetect}
      disabled={disabled || isDetecting || !autoDetectEnabled}
      className={`h-7 px-2 text-xs hover:bg-luxury-gold/10 ${
        autoDetectEnabled
          ? 'text-luxury-gold hover:text-luxury-gold'
          : 'text-foreground/40 hover:text-foreground/60'
      }`}
      title={autoDetectEnabled ? "Auto-detect language" : "Auto-detect disabled"}
    >
      {isDetecting ? (
        <>
          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
          Detecting...
        </>
      ) : (
        <>
          <Sparkles className={`h-3 w-3 mr-1 ${autoDetectEnabled ? '' : 'opacity-50'}`} />
          Auto
        </>
      )}
    </Button>
  );
};
