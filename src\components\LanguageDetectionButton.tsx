import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Language {
  code: string;
  name: string;
  flag: string;
}

interface LanguageDetectionButtonProps {
  isDetecting: boolean;
  detectedLanguage: string | null;
  onDetect: () => void;
  onLanguageDetected: (language: Language) => void;
  availableLanguages: Language[];
  disabled?: boolean;
}

export const LanguageDetectionButton = ({
  isDetecting,
  detectedLanguage,
  onDetect,
  onLanguageDetected,
  availableLanguages,
  disabled = false
}: LanguageDetectionButtonProps) => {
  const { toast } = useToast();

  const handleDetect = () => {
    if (disabled || isDetecting) return;
    onDetect();
  };

  // Коли мова визначена, знаходимо відповідний об'єкт мови
  if (detectedLanguage && !isDetecting) {
    const foundLanguage = availableLanguages.find(lang => lang.code === detectedLanguage);
    if (foundLanguage) {
      // Викликаємо callback з затримкою, щоб дати час на відображення результату
      setTimeout(() => {
        onLanguageDetected(foundLanguage);
        toast({
          title: "Language Detected",
          description: `Detected: ${foundLanguage.name} ${foundLanguage.flag}`,
        });
      }, 500);
    }
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleDetect}
      disabled={disabled || isDetecting}
      className="h-7 px-2 text-xs text-luxury-gold hover:bg-luxury-gold/10 hover:text-luxury-gold"
      title="Auto-detect language"
    >
      {isDetecting ? (
        <>
          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
          Detecting...
        </>
      ) : (
        <>
          <Sparkles className="h-3 w-3 mr-1" />
          Auto
        </>
      )}
    </Button>
  );
};
