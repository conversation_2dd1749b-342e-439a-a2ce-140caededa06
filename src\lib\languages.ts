export const LANGUAGE_MAPPINGS: Record<string, string> = {
  en: "English",
  es: "Spanish", 
  fr: "French",
  de: "German",
  it: "Italian",
  pt: "Portuguese",
  ru: "Russian",
  ja: "Japanese",
  ko: "Korean",
  zh: "Chinese (Simplified)",
  uk: "Ukrainian",
  pl: "Polish",
  nl: "Dutch",
  sv: "Swedish",
  da: "Danish",
  no: "Norwegian",
  fi: "Finnish",
  ar: "Arabic",
  hi: "Hindi",
  th: "Thai",
  vi: "Vietnamese",
  tr: "Turkish",
  he: "Hebrew",
  cs: "Czech",
  hu: "Hungarian",
  ro: "Romanian",
  bg: "Bulgarian",
  hr: "Croatian",
  sk: "Slovak",
  sl: "Slovenian",
  et: "Estonian",
  lv: "Latvian",
  lt: "Lithuanian",
  mt: "Maltese",
  ga: "Irish",
  cy: "Welsh",
  is: "Icelandic",
  mk: "Macedonian",
  sr: "Serbian",
  bs: "Bosnian",
  sq: "Albanian",
  eu: "Basque",
  ca: "Catalan",
  gl: "Galician",
  be: "Belarusian"
};

export const getLanguageName = (code: string): string => {
  return LANGUAGE_MAPPINGS[code] || code;
};