import { useState, useEffect, useCallback } from "react";
import { geminiTranslator } from "@/lib/gemini";

interface UseLanguageDetectionOptions {
  enabled: boolean;
  debounceMs?: number;
  minLength?: number;
}

interface UseLanguageDetectionResult {
  detectedLanguage: string | null;
  isDetecting: boolean;
  error: string | null;
  detectLanguage: (text: string) => void;
  resetDetection: () => void;
}

export const useLanguageDetection = (
  options: UseLanguageDetectionOptions
): UseLanguageDetectionResult => {
  const { enabled, debounceMs = 1000, minLength = 10 } = options;
  
  const [detectedLanguage, setDetectedLanguage] = useState<string | null>(null);
  const [isDetecting, setIsDetecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const resetDetection = useCallback(() => {
    setDetectedLanguage(null);
    setError(null);
    if (debounceTimer) {
      clearTimeout(debounceTimer);
      setDebounceTimer(null);
    }
  }, [debounceTimer]);

  const detectLanguage = useCallback(async (text: string) => {
    if (!enabled || text.trim().length < minLength) {
      setDetectedLanguage(null);
      return;
    }

    // Очищуємо попередній таймер
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Встановлюємо новий таймер
    const timer = setTimeout(async () => {
      setIsDetecting(true);
      setError(null);

      try {
        const result = await geminiTranslator.detectLanguage(text);
        setDetectedLanguage(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Detection failed");
        setDetectedLanguage(null);
      } finally {
        setIsDetecting(false);
      }
    }, debounceMs);

    setDebounceTimer(timer);
  }, [enabled, minLength, debounceMs, debounceTimer]);

  // Очищуємо таймер при unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return {
    detectedLanguage,
    isDetecting,
    error,
    detectLanguage,
    resetDetection
  };
};
