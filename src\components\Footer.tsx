import { useState, useEffect } from "react";

export const Footer = () => {
  const [currentModel, setCurrentModel] = useState<string>("gemini-2.5-flash");

  useEffect(() => {
    const handleModelChange = (event: CustomEvent) => {
      setCurrentModel(event.detail.model);
    };

    window.addEventListener('modelUsed', handleModelChange as EventListener);
    
    return () => {
      window.removeEventListener('modelUsed', handleModelChange as EventListener);
    };
  }, []);

  return (
    <footer className="mt-8 py-6 border-t border-luxury-dark-border">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-center relative">
          <div className="absolute left-0">
            <span className="text-xs text-foreground/40 font-body">
              Powered by {currentModel}
            </span>
          </div>
          
          <div className="text-center">
            <h3 className="text-lg font-luxury font-semibold text-luxury-gold">
              Luxe Translator
            </h3>
            <p className="text-xs text-foreground/50 font-body mt-1">
              Premium translation experience
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};