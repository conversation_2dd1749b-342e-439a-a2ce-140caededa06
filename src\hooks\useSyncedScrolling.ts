import { useEffect, RefObject } from 'react';

export const useSyncedScrolling = (
  ref1: RefObject<HTMLElement>,
  ref2: RefObject<HTMLElement>,
  isSyncEnabled: boolean
) => {
  useEffect(() => {
    const element1 = ref1.current;
    const element2 = ref2.current;

    if (!element1 || !element2 || !isSyncEnabled) {
      return;
    }

    let isSyncing = false;

    const handleScroll = (scrolledElement: HTMLElement, targetElement: HTMLElement) => {
      if (isSyncing) return;
      isSyncing = true;

      const { scrollTop, scrollHeight, clientHeight } = scrolledElement;
      const scrollRatio = scrollTop / (scrollHeight - clientHeight);
      
      targetElement.scrollTop = scrollRatio * (targetElement.scrollHeight - targetElement.clientHeight);
      
      requestAnimationFrame(() => {
        isSyncing = false;
      });
    };

    const scrollListener1 = () => handleScroll(element1, element2);
    const scrollListener2 = () => handleScroll(element2, element1);

    element1.addEventListener('scroll', scrollListener1);
    element2.addEventListener('scroll', scrollListener2);

    return () => {
      element1.removeEventListener('scroll', scrollListener1);
      element2.removeEventListener('scroll', scrollListener2);
    };
  }, [ref1, ref2, isSyncEnabled]);
};
