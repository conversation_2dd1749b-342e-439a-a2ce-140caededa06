import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useModelStats } from '@/hooks/useModelStats';
import { useToast } from '@/hooks/use-toast';
import { 
  BarChart3, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  RotateCcw,
  ChevronDown,
  ChevronUp,
  Shield,
  Zap
} from 'lucide-react';

export const ModelStatsDisplay = () => {
  const { stats, lastFallback, lastFailure, resetStats } = useModelStats();
  const { toast } = useToast();
  const [isExpanded, setIsExpanded] = useState(false);

  const totalRequests = stats.primarySuccess + stats.fallbackSuccess + stats.totalFailures;
  const successRate = totalRequests > 0 ? ((stats.primarySuccess + stats.fallbackSuccess) / totalRequests * 100).toFixed(1) : '0';

  const handleReset = () => {
    resetStats();
    toast({
      title: "Statistics Reset",
      description: "Model usage statistics have been cleared.",
    });
  };

  const handleShowLastError = () => {
    if (lastFailure) {
      toast({
        title: "Last Translation Error",
        description: `${lastFailure.reason}: ${lastFailure.lastError}`,
        variant: "destructive"
      });
    }
  };

  if (totalRequests === 0) {
    return null; // Не показуємо компонент, якщо немає статистики
  }

  return (
    <Card className="bg-luxury-black/50 border-luxury-dark-border">
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-luxury-gold" />
            <span className="text-sm font-medium text-foreground/80">Model Stats</span>
            <Badge variant="outline" className="text-xs">
              {totalRequests} requests
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleReset}
              className="h-7 px-2 text-xs text-foreground/60 hover:text-foreground/80"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-7 px-2 text-xs text-foreground/60 hover:text-foreground/80"
            >
              {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="flex items-center gap-4 mt-3">
          <div className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3 text-green-500" />
            <span className="text-xs text-foreground/70">Success: {successRate}%</span>
          </div>
          
          {stats.prohibitedContent > 0 && (
            <div className="flex items-center gap-1">
              <Shield className="h-3 w-3 text-orange-500" />
              <span className="text-xs text-foreground/70">Blocked: {stats.prohibitedContent}</span>
            </div>
          )}
          
          {stats.fallbackSuccess > 0 && (
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3 text-blue-500" />
              <span className="text-xs text-foreground/70">Fallback: {stats.fallbackSuccess}</span>
            </div>
          )}
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div className="mt-4 space-y-3 border-t border-luxury-dark-border pt-3">
            {/* Detailed Stats */}
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="flex justify-between">
                <span className="text-foreground/60">Primary Success:</span>
                <span className="text-green-500 font-medium">{stats.primarySuccess}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-foreground/60">Fallback Success:</span>
                <span className="text-blue-500 font-medium">{stats.fallbackSuccess}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-foreground/60">Content Blocked:</span>
                <span className="text-orange-500 font-medium">{stats.prohibitedContent}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-foreground/60">Total Failures:</span>
                <span className="text-red-500 font-medium">{stats.totalFailures}</span>
              </div>
            </div>

            {/* Last Events */}
            {(lastFallback || lastFailure) && (
              <div className="space-y-2">
                {lastFallback && (
                  <div className="flex items-center gap-2 p-2 bg-blue-500/10 rounded text-xs">
                    <AlertTriangle className="h-3 w-3 text-blue-500" />
                    <span className="text-foreground/70">
                      Last fallback: {lastFallback.reason} (attempt {lastFallback.attempt})
                    </span>
                  </div>
                )}
                
                {lastFailure && (
                  <div className="flex items-center justify-between p-2 bg-red-500/10 rounded text-xs">
                    <div className="flex items-center gap-2">
                      <XCircle className="h-3 w-3 text-red-500" />
                      <span className="text-foreground/70">
                        Last failure: {lastFailure.reason}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleShowLastError}
                      className="h-5 px-2 text-xs text-red-400 hover:text-red-300"
                    >
                      Details
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};
