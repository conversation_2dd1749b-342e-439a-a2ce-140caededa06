import { GoogleGenerativeAI } from "@google/generative-ai";

const API_KEY = import.meta.env.VITE_GEMINI_API_KEY || "YOUR_GEMINI_API_KEY";

const PRIMARY_MODEL = "gemini-2.5-flash";
const FALLBACK_MODEL = "gemini-2.5-flash-lite";

const notifyModelUsed = (model: string) => {
  window.dispatchEvent(new CustomEvent('modelUsed', { 
    detail: { model } 
  }));
};

export class GeminiTranslator {
  private genAI: GoogleGenerativeAI;
  private languageCache: Map<string, string> = new Map();

  constructor() {
    this.genAI = new GoogleGenerativeAI(API_KEY);
  }

  async detectLanguage(text: string): Promise<string | null> {
    // Мінімальна довжина тексту для визначення
    if (text.trim().length < 10) {
      return null;
    }

    // Перевіряємо кеш
    const cacheKey = text.trim().toLowerCase().substring(0, 100);
    if (this.languageCache.has(cacheKey)) {
      return this.languageCache.get(cacheKey)!;
    }

    // Список доступних мов для визначення
    const availableLanguages = [
      "English", "Ukrainian", "Spanish", "French", "German", "Italian",
      "Portuguese", "Russian", "Japanese", "Korean", "Chinese", "Polish",
      "Dutch", "Swedish", "Arabic", "Hindi", "Turkish"
    ];

    const systemInstruction = `You are a language detection expert. Analyze the given text and identify its language from this list: ${availableLanguages.join(", ")}.

Rules:
1. Return ONLY the language name from the list above
2. If the language is not in the list, return "Unknown"
3. Be confident in your detection
4. For mixed languages, return the dominant language
5. No explanations, just the language name`;

    const prompt = `Detect the language of this text: "${text.substring(0, 200)}"`;

    try {
      // Використовуємо тільки fallback модель для економії
      const model = this.genAI.getGenerativeModel({
        model: FALLBACK_MODEL,
        systemInstruction: systemInstruction
      });

      const result = await model.generateContent(prompt);
      const response = result.response.text().trim();

      // Мапимо відповідь на код мови
      const languageMap: Record<string, string> = {
        "English": "en",
        "Ukrainian": "uk",
        "Spanish": "es",
        "French": "fr",
        "German": "de",
        "Italian": "it",
        "Portuguese": "pt",
        "Russian": "ru",
        "Japanese": "ja",
        "Korean": "ko",
        "Chinese": "zh",
        "Polish": "pl",
        "Dutch": "nl",
        "Swedish": "sv",
        "Arabic": "ar",
        "Hindi": "hi",
        "Turkish": "tr"
      };

      const detectedCode = languageMap[response] || null;

      // Зберігаємо в кеш
      if (detectedCode) {
        this.languageCache.set(cacheKey, detectedCode);
      }

      return detectedCode;
    } catch (error) {
      console.warn("Language detection failed:", error);
      return null;
    }
  }

  async translate(text: string, fromLang: string, toLang: string, stylePrompt?: string): Promise<string> {
    const basePrompt = `Translate the following text from ${fromLang} to ${toLang}. Return only the translation without any additional text or explanations`;
    
    const systemInstruction = stylePrompt 
      ? `${stylePrompt}\n\n${basePrompt}` 
      : basePrompt;

    const prompt = `Text to translate: ${text}`;

    try {
      const primaryModel = this.genAI.getGenerativeModel({ 
        model: PRIMARY_MODEL,
        systemInstruction: systemInstruction
      });
      const result = await primaryModel.generateContent(prompt);
      const response = result.response;
      notifyModelUsed(PRIMARY_MODEL);
      return response.text();
    } catch (primaryError) {
      console.warn(`Primary model ${PRIMARY_MODEL} failed, trying fallback:`, primaryError);
      
      try {
        const fallbackModel = this.genAI.getGenerativeModel({ 
          model: FALLBACK_MODEL,
          systemInstruction: systemInstruction
        });
        const result = await fallbackModel.generateContent(prompt);
        const response = result.response;
        notifyModelUsed(FALLBACK_MODEL);
        return response.text();
      } catch (fallbackError) {
        console.error(`Both models failed. Primary: ${primaryError}, Fallback: ${fallbackError}`);
        throw new Error("Translation failed with both models. Please try again.");
      }
    }
  }

  async translateStream(
    text: string, 
    fromLang: string, 
    toLang: string, 
    onChunk: (chunk: string) => void,
    stylePrompt?: string
  ): Promise<void> {
    const basePrompt = `Translate the following text from ${fromLang} to ${toLang}. Return only the translation without any additional text or explanations`;
    
    const systemInstruction = stylePrompt 
      ? `${stylePrompt}\n\n${basePrompt}` 
      : basePrompt;

    const prompt = `Text to translate: ${text}`;

    try {
      const primaryModel = this.genAI.getGenerativeModel({ 
        model: PRIMARY_MODEL,
        systemInstruction: systemInstruction
      });
      const result = await primaryModel.generateContentStream(prompt);
      notifyModelUsed(PRIMARY_MODEL);
      
      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        if (chunkText) {
          onChunk(chunkText);
        }
      }
    } catch (primaryError) {
      console.warn(`Primary model ${PRIMARY_MODEL} failed for streaming, trying fallback:`, primaryError);
      
      try {
        const fallbackModel = this.genAI.getGenerativeModel({ 
          model: FALLBACK_MODEL,
          systemInstruction: systemInstruction
        });
        const result = await fallbackModel.generateContentStream(prompt);
        notifyModelUsed(FALLBACK_MODEL);
        
        for await (const chunk of result.stream) {
          const chunkText = chunk.text();
          if (chunkText) {
            onChunk(chunkText);
          }
        }
      } catch (fallbackError) {
        console.error(`Both models failed for streaming. Primary: ${primaryError}, Fallback: ${fallbackError}`);
        throw new Error("Translation failed with both models. Please try again.");
      }
    }
  }
}

export const geminiTranslator = new GeminiTranslator();