import { GoogleGenerativeAI } from "@google/generative-ai";

const API_KEY = import.meta.env.VITE_GEMINI_API_KEY || "YOUR_GEMINI_API_KEY";

const PRIMARY_MODEL = "gemini-2.5-flash";
const FALLBACK_MODEL = "gemini-2.5-flash-lite";

const notifyModelUsed = (model: string) => {
  window.dispatchEvent(new CustomEvent('modelUsed', { 
    detail: { model } 
  }));
};

export class GeminiTranslator {
  private genAI: GoogleGenerativeAI;

  constructor() {
    this.genAI = new GoogleGenerativeAI(API_KEY);
  }

  async translate(text: string, fromLang: string, toLang: string, stylePrompt?: string): Promise<string> {
    const basePrompt = `Translate the following text from ${fromLang} to ${toLang}. Return only the translation without any additional text or explanations`;
    
    const systemInstruction = stylePrompt 
      ? `${stylePrompt}\n\n${basePrompt}` 
      : basePrompt;

    const prompt = `Text to translate: ${text}`;

    try {
      const primaryModel = this.genAI.getGenerativeModel({ 
        model: PRIMARY_MODEL,
        systemInstruction: systemInstruction
      });
      const result = await primaryModel.generateContent(prompt);
      const response = result.response;
      notifyModelUsed(PRIMARY_MODEL);
      return response.text();
    } catch (primaryError) {
      console.warn(`Primary model ${PRIMARY_MODEL} failed, trying fallback:`, primaryError);
      
      try {
        const fallbackModel = this.genAI.getGenerativeModel({ 
          model: FALLBACK_MODEL,
          systemInstruction: systemInstruction
        });
        const result = await fallbackModel.generateContent(prompt);
        const response = result.response;
        notifyModelUsed(FALLBACK_MODEL);
        return response.text();
      } catch (fallbackError) {
        console.error(`Both models failed. Primary: ${primaryError}, Fallback: ${fallbackError}`);
        throw new Error("Translation failed with both models. Please try again.");
      }
    }
  }

  async translateStream(
    text: string, 
    fromLang: string, 
    toLang: string, 
    onChunk: (chunk: string) => void,
    stylePrompt?: string
  ): Promise<void> {
    const basePrompt = `Translate the following text from ${fromLang} to ${toLang}. Return only the translation without any additional text or explanations`;
    
    const systemInstruction = stylePrompt 
      ? `${stylePrompt}\n\n${basePrompt}` 
      : basePrompt;

    const prompt = `Text to translate: ${text}`;

    try {
      const primaryModel = this.genAI.getGenerativeModel({ 
        model: PRIMARY_MODEL,
        systemInstruction: systemInstruction
      });
      const result = await primaryModel.generateContentStream(prompt);
      notifyModelUsed(PRIMARY_MODEL);
      
      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        if (chunkText) {
          onChunk(chunkText);
        }
      }
    } catch (primaryError) {
      console.warn(`Primary model ${PRIMARY_MODEL} failed for streaming, trying fallback:`, primaryError);
      
      try {
        const fallbackModel = this.genAI.getGenerativeModel({ 
          model: FALLBACK_MODEL,
          systemInstruction: systemInstruction
        });
        const result = await fallbackModel.generateContentStream(prompt);
        notifyModelUsed(FALLBACK_MODEL);
        
        for await (const chunk of result.stream) {
          const chunkText = chunk.text();
          if (chunkText) {
            onChunk(chunkText);
          }
        }
      } catch (fallbackError) {
        console.error(`Both models failed for streaming. Primary: ${primaryError}, Fallback: ${fallbackError}`);
        throw new Error("Translation failed with both models. Please try again.");
      }
    }
  }
}

export const geminiTranslator = new GeminiTranslator();