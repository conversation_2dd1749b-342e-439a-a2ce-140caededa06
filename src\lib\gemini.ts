import { GoogleGenerativeAI, GenerativeModel } from "@google/generative-ai";

const API_KEY = import.meta.env.VITE_GEMINI_API_KEY || "YOUR_GEMINI_API_KEY";

const PRIMARY_MODEL = "gemini-2.5-flash";
const FALLBACK_MODEL = "gemini-2.5-flash-lite";
const MAX_FALLBACK_RETRIES = 3;

interface RetryOptions {
  maxRetries: number;
  model: string;
  attempt: number;
}

const notifyModelUsed = (model: string, attempt?: number) => {
  window.dispatchEvent(new CustomEvent('modelUsed', {
    detail: { model, attempt }
  }));
};

// Повідомляє про використання фалбек моделі через PROHIBITED_CONTENT
const notifyFallbackUsed = (reason: string, attempt: number) => {
  window.dispatchEvent(new CustomEvent('fallbackUsed', {
    detail: { reason, attempt, model: FALLBACK_MODEL }
  }));
};

// Перевіряє чи є помилка пов'язана з PROHIBITED_CONTENT
const isProhibitedContentError = (error: unknown): boolean => {
  const errorMessage = (error as Error)?.message?.toLowerCase() || '';
  const errorString = error?.toString?.()?.toLowerCase() || '';

  return errorMessage.includes('prohibited_content') ||
         errorMessage.includes('safety') ||
         errorMessage.includes('blocked') ||
         errorString.includes('prohibited_content') ||
         errorString.includes('safety') ||
         errorString.includes('blocked');
};

// Перевіряє чи є помилка критичною (не варто ретраїти)
const isCriticalError = (error: unknown): boolean => {
  const errorMessage = (error as Error)?.message?.toLowerCase() || '';

  return errorMessage.includes('api_key') ||
         errorMessage.includes('quota') ||
         errorMessage.includes('billing') ||
         errorMessage.includes('authentication');
};

export class GeminiTranslator {
  private genAI: GoogleGenerativeAI;
  private languageCache: Map<string, string> = new Map();
  private modelStats = {
    primarySuccess: 0,
    fallbackSuccess: 0,
    prohibitedContent: 0,
    totalFailures: 0
  };

  constructor() {
    this.genAI = new GoogleGenerativeAI(API_KEY);
  }

  // Отримати статистику використання моделей
  getModelStats() {
    return { ...this.modelStats };
  }

  // Скинути статистику
  resetModelStats() {
    this.modelStats = {
      primarySuccess: 0,
      fallbackSuccess: 0,
      prohibitedContent: 0,
      totalFailures: 0
    };
  }

  // Функція для виконання запиту з фалбеком та ретраями
  private async executeWithFallback<T>(
    operation: (model: GenerativeModel, attempt: number) => Promise<T>,
    systemInstruction: string
  ): Promise<T> {
    // Спочатку пробуємо основну модель
    try {
      const primaryModel = this.genAI.getGenerativeModel({
        model: PRIMARY_MODEL,
        systemInstruction: systemInstruction
      });
      const result = await operation(primaryModel, 1);
      notifyModelUsed(PRIMARY_MODEL, 1);
      this.modelStats.primarySuccess++;
      return result;
    } catch (primaryError) {
      console.warn(`Primary model ${PRIMARY_MODEL} failed:`, primaryError);

      // Якщо це критична помилка, не пробуємо фалбек
      if (isCriticalError(primaryError)) {
        throw new Error(`Critical error with primary model: ${(primaryError as Error).message}`);
      }

      // Якщо це PROHIBITED_CONTENT або інша помилка, пробуємо фалбек модель з ретраями
      return await this.retryWithFallback(operation, systemInstruction, primaryError);
    }
  }

  // Функція для ретраїв з фалбек моделлю
  private async retryWithFallback<T>(
    operation: (model: GenerativeModel, attempt: number) => Promise<T>,
    systemInstruction: string,
    originalError: unknown
  ): Promise<T> {
    let lastError = originalError;

    for (let attempt = 1; attempt <= MAX_FALLBACK_RETRIES; attempt++) {
      try {
        console.log(`Trying fallback model ${FALLBACK_MODEL}, attempt ${attempt}/${MAX_FALLBACK_RETRIES}`);

        const fallbackModel = this.genAI.getGenerativeModel({
          model: FALLBACK_MODEL,
          systemInstruction: systemInstruction
        });

        const result = await operation(fallbackModel, attempt);
        notifyModelUsed(FALLBACK_MODEL, attempt);
        this.modelStats.fallbackSuccess++;

        // Якщо успішно, повідомляємо про використання фалбек моделі
        if (isProhibitedContentError(originalError)) {
          console.log(`Successfully used fallback model after PROHIBITED_CONTENT error on attempt ${attempt}`);
          notifyFallbackUsed('PROHIBITED_CONTENT', attempt);
          this.modelStats.prohibitedContent++;
        } else {
          notifyFallbackUsed('PRIMARY_MODEL_FAILED', attempt);
        }

        return result;
      } catch (fallbackError) {
        console.warn(`Fallback model attempt ${attempt} failed:`, fallbackError);
        lastError = fallbackError;

        // Якщо це критична помилка, припиняємо ретраї
        if (isCriticalError(fallbackError)) {
          break;
        }

        // Додаємо затримку між ретраями (експоненційна затримка)
        if (attempt < MAX_FALLBACK_RETRIES) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Максимум 5 секунд
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // Якщо всі спроби провалилися
    const isProhibited = isProhibitedContentError(originalError);
    const isLastProhibited = isProhibitedContentError(lastError);

    let errorMessage: string;
    if (isProhibited || isLastProhibited) {
      errorMessage = "Content was blocked by safety filters. Please try rephrasing your text or using a different translation style.";
    } else if (isCriticalError(lastError)) {
      errorMessage = `Critical error: ${(lastError as Error).message}`;
    } else {
      errorMessage = `Translation failed after ${MAX_FALLBACK_RETRIES} attempts with fallback model. Please try again later.`;
    }

    console.error(`All fallback attempts failed. Original: ${originalError}, Last: ${lastError}`);

    // Оновлюємо статистику
    this.modelStats.totalFailures++;
    if (isProhibited || isLastProhibited) {
      this.modelStats.prohibitedContent++;
    }

    // Повідомляємо про повну невдачу
    window.dispatchEvent(new CustomEvent('translationFailed', {
      detail: {
        reason: isProhibited || isLastProhibited ? 'PROHIBITED_CONTENT' : 'FALLBACK_EXHAUSTED',
        attempts: MAX_FALLBACK_RETRIES,
        originalError: (originalError as Error)?.message,
        lastError: (lastError as Error)?.message
      }
    }));

    throw new Error(errorMessage);
  }

  async detectLanguage(text: string): Promise<string | null> {
    // Мінімальна довжина тексту для визначення
    if (text.trim().length < 10) {
      return null;
    }

    // Перевіряємо кеш
    const cacheKey = text.trim().toLowerCase().substring(0, 100);
    if (this.languageCache.has(cacheKey)) {
      return this.languageCache.get(cacheKey)!;
    }

    // Список доступних мов для визначення
    const availableLanguages = [
      "English", "Ukrainian", "Spanish", "French", "German", "Italian",
      "Portuguese", "Russian", "Japanese", "Korean", "Chinese", "Polish",
      "Dutch", "Swedish", "Arabic", "Hindi", "Turkish"
    ];

    const systemInstruction = `You are a language detection expert. Analyze the given text and identify its language from this list: ${availableLanguages.join(", ")}.

Rules:
1. Return ONLY the language name from the list above
2. If the language is not in the list, return "Unknown"
3. Be confident in your detection
4. For mixed languages, return the dominant language
5. No explanations, just the language name`;

    const prompt = `Detect the language of this text: "${text.substring(0, 200)}"`;

    try {
      // Використовуємо тільки fallback модель для економії
      const model = this.genAI.getGenerativeModel({
        model: FALLBACK_MODEL,
        systemInstruction: systemInstruction
      });

      const result = await model.generateContent(prompt);
      const response = result.response.text().trim();

      // Мапимо відповідь на код мови
      const languageMap: Record<string, string> = {
        "English": "en",
        "Ukrainian": "uk",
        "Spanish": "es",
        "French": "fr",
        "German": "de",
        "Italian": "it",
        "Portuguese": "pt",
        "Russian": "ru",
        "Japanese": "ja",
        "Korean": "ko",
        "Chinese": "zh",
        "Polish": "pl",
        "Dutch": "nl",
        "Swedish": "sv",
        "Arabic": "ar",
        "Hindi": "hi",
        "Turkish": "tr"
      };

      const detectedCode = languageMap[response] || null;

      // Зберігаємо в кеш
      if (detectedCode) {
        this.languageCache.set(cacheKey, detectedCode);
      }

      return detectedCode;
    } catch (error) {
      console.warn("Language detection failed:", error);
      return null;
    }
  }

  async translate(text: string, fromLang: string, toLang: string, stylePrompt?: string): Promise<string> {
    const basePrompt = `Translate the following text from ${fromLang} to ${toLang}. Return only the translation without any additional text or explanations`;

    const systemInstruction = stylePrompt
      ? `${stylePrompt}\n\n${basePrompt}`
      : basePrompt;

    const prompt = `Text to translate: ${text}`;

    return await this.executeWithFallback(
      async (model: GenerativeModel) => {
        const result = await model.generateContent(prompt);
        return result.response.text();
      },
      systemInstruction
    );
  }

  async translateStream(
    text: string,
    fromLang: string,
    toLang: string,
    onChunk: (chunk: string) => void,
    stylePrompt?: string
  ): Promise<void> {
    const basePrompt = `Translate the following text from ${fromLang} to ${toLang}. Return only the translation without any additional text or explanations`;

    const systemInstruction = stylePrompt
      ? `${stylePrompt}\n\n${basePrompt}`
      : basePrompt;

    const prompt = `Text to translate: ${text}`;

    await this.executeWithFallback(
      async (model: GenerativeModel) => {
        const result = await model.generateContentStream(prompt);

        for await (const chunk of result.stream) {
          const chunkText = chunk.text();
          if (chunkText) {
            onChunk(chunkText);
          }
        }

        return; // Повертаємо void
      },
      systemInstruction
    );
  }
}

export const geminiTranslator = new GeminiTranslator();