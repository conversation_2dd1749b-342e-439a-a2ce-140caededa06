import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LanguageDetectionButton } from "@/components/LanguageDetectionButton";

interface Language {
  code: string;
  name: string;
  flag: string;
}

export const languages: Language[] = [
  { code: "en", name: "English", flag: "🇺🇸" },
  { code: "uk", name: "Ukrainian", flag: "🇺🇦" },
  { code: "es", name: "Spanish", flag: "🇪🇸" },
  { code: "fr", name: "French", flag: "🇫🇷" },
  { code: "de", name: "German", flag: "🇩🇪" },
  { code: "it", name: "Italian", flag: "🇮🇹" },
  { code: "pt", name: "Portuguese", flag: "🇵🇹" },
  { code: "ru", name: "Russian", flag: "🇷🇺" },
  { code: "ja", name: "Japanese", flag: "🇯🇵" },
  { code: "ko", name: "Korean", flag: "🇰🇷" },
  { code: "zh", name: "Chinese", flag: "🇨🇳" },
  { code: "pl", name: "Polish", flag: "🇵🇱" },
  { code: "nl", name: "Dutch", flag: "🇳🇱" },
  { code: "sv", name: "Swedish", flag: "🇸🇪" },
  { code: "ar", name: "Arabic", flag: "🇸🇦" },
  { code: "hi", name: "Hindi", flag: "🇮🇳" },
  { code: "tr", name: "Turkish", flag: "🇹🇷" },
];

interface LanguageSelectorProps {
  selectedLanguage: Language;
  onLanguageChange: (language: Language) => void;
  label: string;
  showAutoDetect?: boolean;
  onAutoDetect?: () => void;
  isDetecting?: boolean;
  detectedLanguage?: string | null;
}

export const LanguageSelector = ({
  selectedLanguage,
  onLanguageChange,
  label,
  showAutoDetect = false,
  onAutoDetect,
  isDetecting = false,
  detectedLanguage
}: LanguageSelectorProps) => {
  return (
    <div className="space-y-1.5">
      <div className="flex items-center justify-between">
        <label className="text-xs font-luxury font-medium text-foreground/70 uppercase tracking-wide">{label}</label>
        {showAutoDetect && onAutoDetect && (
          <LanguageDetectionButton
            isDetecting={isDetecting}
            detectedLanguage={detectedLanguage}
            onDetect={onAutoDetect}
            onLanguageDetected={onLanguageChange}
            availableLanguages={languages}
          />
        )}
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="luxury" className="w-full justify-between h-9 px-3 text-sm">
            <span className="flex items-center gap-2">
              <span className="text-sm">{selectedLanguage.flag}</span>
              <span className="font-body text-sm">{selectedLanguage.name}</span>
            </span>
            <ChevronDown className="h-3.5 w-3.5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-48 bg-luxury-charcoal border-luxury-dark-border">
          {languages.map((language) => (
            <DropdownMenuItem
              key={language.code}
              onClick={() => onLanguageChange(language)}
              className="flex items-center gap-2 text-foreground hover:bg-luxury-gold/10 focus:bg-luxury-gold/10 cursor-pointer py-2"
            >
              <span className="text-sm">{language.flag}</span>
              <span className="font-body text-sm">{language.name}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};