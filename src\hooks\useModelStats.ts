import { useState, useEffect } from 'react';
import { geminiTranslator } from '@/lib/gemini';

interface ModelStats {
  primarySuccess: number;
  fallbackSuccess: number;
  prohibitedContent: number;
  totalFailures: number;
}

interface FallbackEvent {
  reason: string;
  attempt: number;
  model: string;
}

interface TranslationFailedEvent {
  reason: string;
  attempts: number;
  originalError: string;
  lastError: string;
}

export const useModelStats = () => {
  const [stats, setStats] = useState<ModelStats>(() => geminiTranslator.getModelStats());
  const [lastFallback, setLastFallback] = useState<FallbackEvent | null>(null);
  const [lastFailure, setLastFailure] = useState<TranslationFailedEvent | null>(null);

  useEffect(() => {
    const handleFallbackUsed = (event: CustomEvent<FallbackEvent>) => {
      setLastFallback(event.detail);
      setStats(geminiTranslator.getModelStats());
    };

    const handleTranslationFailed = (event: CustomEvent<TranslationFailedEvent>) => {
      setLastFailure(event.detail);
      setStats(geminiTranslator.getModelStats());
    };

    const handleModelUsed = () => {
      setStats(geminiTranslator.getModelStats());
    };

    // Додаємо слухачі подій
    window.addEventListener('fallbackUsed', handleFallbackUsed as EventListener);
    window.addEventListener('translationFailed', handleTranslationFailed as EventListener);
    window.addEventListener('modelUsed', handleModelUsed as EventListener);

    return () => {
      window.removeEventListener('fallbackUsed', handleFallbackUsed as EventListener);
      window.removeEventListener('translationFailed', handleTranslationFailed as EventListener);
      window.removeEventListener('modelUsed', handleModelUsed as EventListener);
    };
  }, []);

  const resetStats = () => {
    geminiTranslator.resetModelStats();
    setStats(geminiTranslator.getModelStats());
    setLastFallback(null);
    setLastFailure(null);
  };

  return {
    stats,
    lastFallback,
    lastFailure,
    resetStats
  };
};
