@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dolce & Gabbana inspired luxury design system - bold dark with gold accents */

@layer base {
  :root {
    /* Luxury dark theme with gold accents */
    --background: 220 13% 8%;
    --foreground: 38 90% 85%;

    --card: 220 13% 12%;
    --card-foreground: 38 90% 85%;

    --popover: 220 13% 10%;
    --popover-foreground: 38 90% 85%;

    --primary: 43 74% 66%;
    --primary-foreground: 220 13% 8%;

    --secondary: 220 13% 16%;
    --secondary-foreground: 38 90% 85%;

    --muted: 220 13% 14%;
    --muted-foreground: 38 30% 65%;

    --accent: 43 100% 75%;
    --accent-foreground: 220 13% 8%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 38 90% 85%;

    --border: 220 13% 20%;
    --input: 220 13% 16%;
    --ring: 43 74% 66%;

    /* Luxury specific tokens */
    --luxury-gold: 43 74% 66%;
    --luxury-gold-light: 43 100% 75%;
    --luxury-gold-dark: 43 60% 50%;
    --luxury-black: 220 13% 8%;
    --luxury-charcoal: 220 13% 12%;
    --luxury-dark-border: 220 13% 20%;
    
    /* Gradients */
    --gradient-luxury: linear-gradient(135deg, hsl(220 13% 8%), hsl(220 13% 12%), hsl(220 13% 16%));
    --gradient-gold: linear-gradient(135deg, hsl(43 74% 66%), hsl(43 100% 75%));
    --gradient-gold-subtle: linear-gradient(135deg, hsl(43 74% 66% / 0.1), hsl(43 100% 75% / 0.2));
    
    /* Shadows */
    --shadow-luxury: 0 25px 50px -12px hsl(220 13% 4% / 0.8);
    --shadow-gold-glow: 0 0 30px hsl(43 74% 66% / 0.3);
    --shadow-elegant: 0 10px 40px -8px hsl(220 13% 4% / 0.6);
    
    /* Typography */
    --font-luxury: 'Playfair Display', serif;
    --font-body: 'Inter', sans-serif;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Luxury Translator Symmetry Enhancements */
@layer components {
  /* Perfect symmetry for translator interface */
  .translator-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: stretch;
    min-height: 600px;
  }

  .translator-column {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .translator-header {
    height: 32px; /* Fixed height for headers */
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-shrink: 0;
  }

  .translator-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Important for flex children */
  }

  .translator-textarea-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 300px;
  }

  .translator-textarea,
  .translator-output {
    flex: 1;
    min-height: 0;
    height: 100%;
    /* Apple-inspired readable typography */
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 400;
    letter-spacing: -0.01em;
    line-height: 1.5;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .translator-counter {
    height: 20px; /* Fixed height for counters */
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 0.5rem;
    flex-shrink: 0;
  }

  .translator-actions {
    height: 48px; /* Fixed height for action buttons */
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
    flex-shrink: 0;
  }

  .translator-spacer {
    height: 64px; /* Match action buttons + margin exactly: 48px (buttons) + 16px (margin) */
    margin-top: 1rem;
    flex-shrink: 0;
    /* Ensure exact alignment */
    box-sizing: border-box;
  }

  /* Ensure perfect alignment and fix right column positioning */
  .translator-grid > * {
    align-self: stretch;
  }

  /* Language selector improvements */
  .language-selector-compact {
    height: 36px; /* Reduced from default */
  }

  .language-selector-compact .dropdown-trigger {
    height: 32px;
    padding: 0 12px;
    font-size: 0.875rem;
  }

  /* Apple-inspired readable text styling */
  .elegant-text {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 400;
    letter-spacing: -0.01em;
    text-rendering: optimizeLegibility;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Apple-style placeholder styling */
  .elegant-text::placeholder {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    letter-spacing: -0.005em;
    opacity: 0.6;
  }

  /* Enhanced readability for text areas */
  .readable-text {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: -0.01em;
    word-spacing: 0.05em;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Improved focus states for better UX */
  .translator-textarea:focus,
  .translator-output:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.3);
    border-color: rgb(212, 175, 55);
  }
}