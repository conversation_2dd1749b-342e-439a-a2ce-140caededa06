import { But<PERSON> } from "@/components/ui/button";
import { Settings } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TranslationStyle, translationStyles } from "@/lib/translationStyles";

interface TranslationStyleSelectorProps {
  selectedStyle: TranslationStyle;
  onStyleChange: (style: TranslationStyle) => void;
}

export const TranslationStyleSelector = ({ 
  selectedStyle, 
  onStyleChange 
}: TranslationStyleSelectorProps) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="luxury" className="flex items-center gap-2 px-4 h-12">
          <Settings className="h-4 w-4 text-luxury-gold" />
          <span className="text-sm">{selectedStyle.icon}</span>
          <span className="font-body text-sm">Style: {selectedStyle.name}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-72 bg-luxury-charcoal border-luxury-dark-border">
        {translationStyles.map((style) => (
          <DropdownMenuItem
            key={style.id}
            onClick={() => onStyleChange(style)}
            className="flex flex-col items-start gap-0.5 p-2 text-foreground hover:bg-luxury-gold/10 focus:bg-luxury-gold/10 cursor-pointer"
          >
            <div className="flex items-center gap-2 w-full">
              <span className="text-base">{style.icon}</span>
              <span className="font-body text-sm">{style.name}</span>
            </div>
            <span className="text-[11px] text-foreground/60 font-body ml-7 leading-tight">
              {style.description}
            </span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
