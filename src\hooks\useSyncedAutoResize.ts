import { useEffect, RefObject } from "react";

export const useSyncedAutoResize = (
  ref1: RefObject<HTMLElement>, 
  ref2: RefObject<HTMLElement>, 
  value1: string, 
  value2: string
) => {
  useEffect(() => {
    const element1 = ref1.current;
    const element2 = ref2.current;

    if (element1 && element2) {
      // Reset heights to auto to get the correct scrollHeight
      element1.style.height = 'auto';
      element2.style.height = 'auto';

      // Calculate new heights based on content
      const height1 = element1.scrollHeight;
      const height2 = element2.scrollHeight;
      
      const newHeight = Math.max(height1, height2, 300); // minimum 300px
      const maxHeight = window.innerHeight * 0.6; // maximum 60% of viewport
      
      const finalHeight = Math.min(newHeight, maxHeight) + 'px';

      element1.style.height = finalHeight;
      element2.style.height = finalHeight;
    }
  }, [ref1, ref2, value1, value2]);
};
