// Утиліта для тестування системи фалбеку
// Використовується тільки для розробки та тестування

export const testFallbackSystem = () => {
  console.log('🧪 Testing Fallback System');
  
  // Симулюємо PROHIBITED_CONTENT помилку
  const simulateProhibitedContent = () => {
    const event = new CustomEvent('fallbackUsed', {
      detail: {
        reason: 'PROHIBITED_CONTENT',
        attempt: 2,
        model: 'gemini-2.5-flash-lite'
      }
    });
    window.dispatchEvent(event);
    console.log('✅ Simulated PROHIBITED_CONTENT fallback');
  };

  // Симулюємо повну невдачу
  const simulateTranslationFailure = () => {
    const event = new CustomEvent('translationFailed', {
      detail: {
        reason: 'PROHIBITED_CONTENT',
        attempts: 3,
        originalError: 'Content blocked by safety filters',
        lastError: 'All fallback attempts failed'
      }
    });
    window.dispatchEvent(event);
    console.log('❌ Simulated translation failure');
  };

  // Симулюємо успішне використання основної моделі
  const simulatePrimarySuccess = () => {
    const event = new CustomEvent('modelUsed', {
      detail: {
        model: 'gemini-2.5-flash',
        attempt: 1
      }
    });
    window.dispatchEvent(event);
    console.log('✅ Simulated primary model success');
  };

  return {
    simulateProhibitedContent,
    simulateTranslationFailure,
    simulatePrimarySuccess
  };
};

// Додаємо до window для легкого доступу в консолі браузера
if (typeof window !== 'undefined') {
  (window as any).testFallback = testFallbackSystem();
}
