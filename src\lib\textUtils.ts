export const getAdaptiveFontSize = (charCount: number): string => {
  // Apple-стиль адаптивного розміру шрифту для оптимальної читабельності
  if (charCount < 100) {
    return "text-lg"; // 18px - для коротких текстів, зручно читати
  } else if (charCount < 500) {
    return "text-base"; // 16px - стандартний розмір для середніх текстів
  } else if (charCount < 1500) {
    return "text-base"; // 16px - залишаємо читабельним для довгих текстів
  } else if (charCount < 3000) {
    return "text-sm"; // 14px - тільки для дуже довгих текстів
  } else {
    return "text-sm"; // 14px - мінімальний читабельний розмір
  }
};

export const getLineHeight = (charCount: number): string => {
  // Apple-стиль міжрядкових інтервалів для оптимальної читабельності
  if (charCount < 500) {
    return "leading-normal"; // 1.5 - комфортно для коротких текстів
  } else if (charCount < 1500) {
    return "leading-normal"; // 1.5 - стандартний Apple інтервал
  } else {
    return "leading-normal"; // 1.5 - залишаємо читабельним навіть для довгих текстів
  }
};

export const getElegantFontClass = (): string => {
  // Повертає класи для більш вишуканого шрифту
  return "font-serif"; // Використовуємо serif шрифт для елегантності
};