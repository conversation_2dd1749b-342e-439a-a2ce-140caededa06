export const getAdaptiveFontSize = (charCount: number): string => {
  // Правила адаптивного розміру шрифту в залежності від кількості символів
  if (charCount < 100) {
    return "text-xl"; // 20px - для коротких текстів
  } else if (charCount < 500) {
    return "text-lg"; // 18px - для середніх текстів  
  } else if (charCount < 1500) {
    return "text-base"; // 16px - для довгих текстів
  } else if (charCount < 3000) {
    return "text-sm"; // 14px - для дуже довгих текстів
  } else {
    return "text-xs"; // 12px - для екстремально довгих текстів
  }
};

export const getLineHeight = (charCount: number): string => {
  // Зменшуємо міжрядковий інтервал для довших текстів
  if (charCount < 500) {
    return "leading-relaxed"; // 1.625
  } else if (charCount < 1500) {
    return "leading-normal"; // 1.5
  } else {
    return "leading-snug"; // 1.375
  }
};