export const getAdaptiveFontSize = (charCount: number): string => {
  // Правила адаптивного розміру шрифту в залежності від кількості символів
  // Зменшили базовий розмір для більш елегантного вигляду
  if (charCount < 100) {
    return "text-lg"; // 18px - для коротких текстів
  } else if (charCount < 500) {
    return "text-base"; // 16px - для середніх текстів
  } else if (charCount < 1500) {
    return "text-sm"; // 14px - для довгих текстів
  } else if (charCount < 3000) {
    return "text-xs"; // 12px - для дуже довгих текстів
  } else {
    return "text-xs"; // 12px - для екстремально довгих текстів
  }
};

export const getLineHeight = (charCount: number): string => {
  // Оптимізовані міжрядкові інтервали для кращої читабельності
  if (charCount < 500) {
    return "leading-relaxed"; // 1.625 - для коротких текстів
  } else if (charCount < 1500) {
    return "leading-normal"; // 1.5 - для середніх текстів
  } else {
    return "leading-snug"; // 1.375 - для довгих текстів
  }
};

export const getElegantFontClass = (): string => {
  // Повертає класи для більш вишуканого шрифту
  return "font-serif"; // Використовуємо serif шрифт для елегантності
};