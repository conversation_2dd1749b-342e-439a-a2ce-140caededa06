import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { LanguageSelector } from "@/components/LanguageSelector";
import { TranslationStyleSelector } from "@/components/TranslationStyleSelector";
import { Footer } from "@/components/Footer";
import { ModelStatsDisplay } from "@/components/ModelStatsDisplay";
import { ArrowLeftRight, Copy, Volume2, Sparkles, Expand, Minimize2, X, Link, Link2Off } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useSyncedAutoResize } from "@/hooks/useSyncedAutoResize";
import { useSyncedScrolling } from "@/hooks/useSyncedScrolling";
import { useLanguageDetection } from "@/hooks/useLanguageDetection";
import { geminiTranslator } from "@/lib/gemini";
import { getLanguageName } from "@/lib/languages";
import { TranslationStyle, getDefaultTranslationStyle } from "@/lib/translationStyles";
import { getAdaptiveFontSize, getLineHeight, getCharCount, getWordCount } from "@/lib/textUtils";

// Імпортуємо тестер тільки в режимі розробки
if (import.meta.env.DEV) {
  import("@/utils/fallbackTester");
}

interface Language {
  code: string;
  name: string;
  flag: string;
}

const defaultFromLanguage: Language = { code: "en", name: "English", flag: "🇺🇸" };
const defaultToLanguage: Language = { code: "uk", name: "Ukrainian", flag: "🇺🇦" };


export const TranslatorInterface = () => {
  const [sourceText, setSourceText] = useState("");
  const [translatedText, setTranslatedText] = useState("");
  const [fromLanguage, setFromLanguage] = useState<Language>(defaultFromLanguage);
  const [toLanguage, setToLanguage] = useState<Language>(defaultToLanguage);
  const [translationStyle, setTranslationStyle] = useState<TranslationStyle>(getDefaultTranslationStyle());
  const [isTranslating, setIsTranslating] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isScrollSynced, setIsScrollSynced] = useState(false);
  const [autoDetectEnabled, setAutoDetectEnabled] = useState(true);
  const { toast } = useToast();

  // Хук для автовизначення мови
  const { detectedLanguage, isDetecting, detectLanguage, resetDetection } = useLanguageDetection({
    enabled: autoDetectEnabled,
    debounceMs: 1500,
    minLength: 15
  });
  
  const sourceTextareaRef = useRef<HTMLTextAreaElement>(null);
  const translatedTextareaRef = useRef<HTMLDivElement>(null);
  useSyncedAutoResize(sourceTextareaRef, translatedTextareaRef, sourceText, translatedText);
  useSyncedScrolling(sourceTextareaRef, translatedTextareaRef, isScrollSynced);

  const toggleScrollSync = () => {
    setIsScrollSynced(!isScrollSynced);
    toast({
      title: `Scroll Sync ${!isScrollSynced ? 'Enabled' : 'Disabled'}`,
      description: `The text areas will ${!isScrollSynced ? 'now' : 'no longer'} scroll together.`,
    });
  };

  const handleAutoDetect = () => {
    if (sourceText.trim()) {
      detectLanguage(sourceText);
    }
  };

  // Автоматичне визначення мови при зміні тексту
  const handleTextChange = (text: string) => {
    setSourceText(text);
    if (autoDetectEnabled && text.trim().length >= 15) {
      detectLanguage(text);
    }
  };

  // Обробка зміни мови вручну - скидаємо автовизначення
  const handleFromLanguageChange = (language: Language) => {
    setFromLanguage(language);
    resetDetection(); // Скидаємо стан автовизначення
  };

  const handleTranslate = async () => {
    if (!sourceText.trim()) return;
    
    setIsTranslating(true);
    setTranslatedText("");
    
    try {
      const fromLangName = getLanguageName(fromLanguage.code);
      const toLangName = getLanguageName(toLanguage.code);
      
      let accumulatedText = "";
      
      await geminiTranslator.translateStream(
        sourceText,
        fromLangName,
        toLangName,
        (chunk) => {
          accumulatedText += chunk;
          setTranslatedText(accumulatedText);
        },
        translationStyle.systemPrompt
      );
      
    } catch (error) {
      console.error("Translation failed:", error);
      toast({
        title: "Translation Failed",
        description: error instanceof Error ? error.message : "An error occurred during translation",
        variant: "destructive"
      });
    } finally {
      setIsTranslating(false);
    }
  };

  const getWordCount = (text: string) => {
    return text.trim() ? text.trim().split(/\s+/).length : 0;
  };

  const getCharCount = (text: string) => {
    return text.length;
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const clearText = () => {
    setSourceText("");
    setTranslatedText("");
  };

  const swapLanguages = () => {
    setFromLanguage(toLanguage);
    setToLanguage(fromLanguage);
    setSourceText(translatedText);
    setTranslatedText(sourceText);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: "Text has been copied successfully",
    });
  };

  const handleSpeak = (text: string) => {
    if ('speechSynthesis' in window && text.trim()) {
      const utterance = new SpeechSynthesisUtterance(text);
      speechSynthesis.speak(utterance);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-luxury">
      {/* Header */}
      <div className="border-b border-luxury-dark-border bg-luxury-black/50 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center gap-3">
            <Sparkles className="h-8 w-8 text-luxury-gold" />
            <h1 className="text-3xl font-luxury font-bold text-luxury-gold">
              Luxe Translator
            </h1>
          </div>
          <p className="mt-2 text-foreground/70 font-body">
            Premium translation experience with elegant design
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <Card className="bg-luxury-charcoal border-luxury-dark-border shadow-luxury">
          {/* Language Selection Header - Compact */}
          <div className="px-6 py-4 pb-2">
            <div className="flex items-end justify-between gap-3">
              <div className="flex-1 max-w-[180px]">
                <LanguageSelector
                  selectedLanguage={fromLanguage}
                  onLanguageChange={handleFromLanguageChange}
                  label="From"
                  showAutoDetect={true}
                  onAutoDetect={handleAutoDetect}
                  isDetecting={isDetecting}
                  detectedLanguage={detectedLanguage}
                  autoDetectEnabled={autoDetectEnabled}
                />
              </div>
              <div className="flex items-center justify-center mt-6">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={swapLanguages}
                  className="h-8 w-8 text-luxury-gold hover:bg-luxury-gold/10 rounded-full"
                >
                  <ArrowLeftRight className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex-1 max-w-[180px]">
                <LanguageSelector
                  selectedLanguage={toLanguage}
                  onLanguageChange={setToLanguage}
                  label="To"
                />
              </div>
            </div>
          </div>

          {/* Main Content Grid - Perfectly Aligned */}
          <div className="translator-grid lg:translator-grid">
            {/* Input Section - Left Side */}
            <div className="translator-column p-6 pt-4 border-r border-luxury-dark-border">
              {/* Input Control Header - Fixed Height */}
              <div className="translator-header">
                <div className="text-sm font-luxury font-medium text-foreground/80">
                  Text Input
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleScrollSync}
                    className={`h-7 w-7 text-foreground/40 hover:text-foreground/60 ${isScrollSynced ? 'text-luxury-gold' : ''}`}
                    title={isScrollSynced ? "Disable Scroll Sync" : "Enable Scroll Sync"}
                  >
                    {isScrollSynced ? <Link className="h-4 w-4" /> : <Link2Off className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setAutoDetectEnabled(!autoDetectEnabled)}
                    className={`h-7 w-7 text-foreground/40 hover:text-foreground/60 ${autoDetectEnabled ? 'text-luxury-gold' : ''}`}
                    title={autoDetectEnabled ? "Disable Auto-detect" : "Enable Auto-detect"}
                  >
                    <Sparkles className="h-4 w-4" />
                  </Button>
                  {sourceText.trim() && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={clearText}
                      className="h-7 w-7 text-foreground/40 hover:text-foreground/60 hover:bg-red-500/10"
                      title="Clear text"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleExpanded}
                    className="h-7 w-7 text-foreground/40 hover:text-foreground/60"
                    title={isExpanded ? "Minimize" : "Expand"}
                  >
                    {isExpanded ? <Minimize2 className="h-4 w-4" /> : <Expand className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Text Area Content */}
              <div className="translator-content">
                <div className="translator-textarea-container">
                  <Textarea
                    ref={sourceTextareaRef}
                    placeholder="Enter text to translate..."
                    value={sourceText}
                    onChange={(e) => handleTextChange(e.target.value)}
                    className={`translator-textarea readable-text ${isExpanded ? 'min-h-[500px]' : 'min-h-[300px]'} bg-luxury-black border-luxury-dark-border text-foreground placeholder:text-foreground/40 resize-none ${getAdaptiveFontSize(getCharCount(sourceText))} ${getLineHeight(getCharCount(sourceText))} focus:ring-luxury-gold`}
                  />
                </div>

                {/* Counter - Fixed Height */}
                <div className="translator-counter text-xs text-foreground/40">
                  {getCharCount(sourceText)} chars, {getWordCount(sourceText)} words
                </div>
              </div>

              {/* Action Buttons - Fixed Height */}
              <div className="translator-actions">
                <TranslationStyleSelector
                  selectedStyle={translationStyle}
                  onStyleChange={setTranslationStyle}
                />

                <Button
                  onClick={handleTranslate}
                  disabled={!sourceText.trim() || isTranslating}
                  className="flex-1 h-12 bg-gradient-gold hover:opacity-90 text-luxury-black font-luxury font-semibold shadow-gold-glow"
                >
                  {isTranslating ? (
                    <>
                      <div className="w-4 h-4 border-2 border-luxury-black border-t-transparent rounded-full animate-spin mr-2"></div>
                      Translating...
                    </>
                  ) : (
                    "Translate"
                  )}
                </Button>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleSpeak(sourceText)}
                  disabled={!sourceText}
                  className="text-luxury-gold hover:bg-luxury-gold/10"
                >
                  <Volume2 className="h-4 w-4" />
                </Button>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => copyToClipboard(sourceText)}
                  disabled={!sourceText}
                  className="text-luxury-gold hover:bg-luxury-gold/10"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Translation Output - Right Side */}
            <div className="translator-column p-6 pt-4">
              {/* Output Control Header - Fixed Height (Same as Input) */}
              <div className="translator-header">
                <div className="text-sm font-luxury font-medium text-foreground/80">
                  Translation Output
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleSpeak(translatedText)}
                    disabled={!translatedText}
                    className="text-luxury-gold hover:bg-luxury-gold/10"
                  >
                    <Volume2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => copyToClipboard(translatedText)}
                    disabled={!translatedText}
                    className="text-luxury-gold hover:bg-luxury-gold/10"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Output Area Content */}
              <div className="translator-content">
                <div className="translator-textarea-container">
                  <div ref={translatedTextareaRef} className={`translator-output readable-text ${isExpanded ? 'min-h-[500px]' : 'min-h-[300px]'} p-4 rounded-lg bg-luxury-black border border-luxury-dark-border overflow-auto`}>
                    {isTranslating ? (
                      <div className="flex items-center justify-center h-full">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 border-2 border-luxury-gold border-t-transparent rounded-full animate-spin"></div>
                          <span className="text-foreground/70 readable-text">Translating...</span>
                        </div>
                      </div>
                    ) : translatedText ? (
                      <div
                        className={`text-foreground readable-text ${getAdaptiveFontSize(getCharCount(translatedText))} ${getLineHeight(getCharCount(translatedText))} whitespace-pre-wrap`}
                      >
                        {translatedText}
                      </div>
                    ) : (
                      <p className="text-foreground/40 readable-text italic">
                        Translation will appear here...
                      </p>
                    )}
                  </div>
                </div>

                {/* Counter - Fixed Height (Same as Input) */}
                <div className="translator-counter text-xs text-foreground/40">
                  {translatedText ? `${getCharCount(translatedText)} chars, ${getWordCount(translatedText)} words` : ''}
                </div>
              </div>

              {/* Spacer to match action buttons height on left side */}
              <div className="translator-spacer"></div>
            </div>
          </div>
        </Card>

        {/* Model Statistics */}
        <div className="mt-6">
          <ModelStatsDisplay />
        </div>

        <Footer />
      </div>
    </div>
  );
};
